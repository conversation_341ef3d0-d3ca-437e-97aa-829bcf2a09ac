import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/order_main_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class GSOrderListViewModel {
  String title;
  String id;
  String logo;
  String? gameClassCode;
  String? categoryCode;
  String returnAmount; // 游戏返水金额
  double betAmount; // 下单金额
  double winAmount; // 盈亏金额

  GSOrderListViewModel({
    required this.title,
    required this.id,
    required this.logo,
    this.gameClassCode,
    this.categoryCode,
    required this.returnAmount,
    required this.betAmount,
    required this.winAmount,
  });

  factory GSOrderListViewModel.formOrderMainEntity(OrderMainEntity model) {
    return GSOrderListViewModel(
        title: model.gameClassName,
        id: model.gameClassCode,
        logo: ChannelType.getLogoByCode(model.gameClassCode),
        returnAmount: model.rebate.formattedMoney,
        betAmount: model.totalBetAmount,
        winAmount: model.totalWin);
  }

  factory GSOrderListViewModel.formOrderPlatformEntity(OrderPlatformEntity model) {
    return GSOrderListViewModel(
        title: model.platFormName,
        id: model.platFormId.toString(),
        gameClassCode: model.gameClassCode,
        categoryCode: model.categoryCode,
        logo: ChannelType.getLogoByCode(model.gameClassCode),
        returnAmount: model.rebate.formattedMoney,
        betAmount: model.totalBetAmount,
        winAmount: model.totalWin);
  }
}

class GSOrderMainListCell extends StatelessWidget {
  final VoidCallback? onPressed;
  final GSOrderListViewModel model;
  final bool showTitle;

  const GSOrderMainListCell({super.key, required this.model, this.onPressed, this.showTitle = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      margin: EdgeInsets.symmetric(horizontal: 8.gw),
      height: 250.gw,
      child: Column(children: [
        HeaderContentCard(
          header: Row(
            children: [
              Text(model.title),
              const Spacer(),
              CommonButton(
                title: 'more'.tr(),
                onPressed: onPressed,
                width: 70,
                height: 30,
                borderColor: Colors.transparent,
                style: CommonButtonStyle.quaternary,
              ),
            ],
          ),
          content: Column(
            children: [
              _buildRow(context, title: 'game_rebate'.tr(), data: model.returnAmount),
              _buildRow(context, title: 'bet_amount'.tr(), data: "¥${model.betAmount.formattedMoney}"),
              _buildRow(context,
                  title: 'total_profit_loss'.tr(), data: '¥${model.winAmount.formattedMoney}', showDivider: false),
            ],
          ),
        ),
      ]),
    );
  }

  _buildRow(
    BuildContext context, {
    required String title,
    required String data,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 20.gh),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              AneText(
                title,
                style: context.textTheme.title.fs15.w600,
              ),
              const Spacer(),
              AneText(
                data,
                style: context.textTheme.title.fs15.w600,
              ),
            ],
          ),
        ),
        if (showDivider)
          Divider(
            color: Theme.of(context).dividerColor,
            height: 1,
          ),
      ],
    );
  }
}
