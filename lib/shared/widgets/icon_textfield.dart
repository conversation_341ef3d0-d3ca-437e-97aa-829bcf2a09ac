import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class IconTextfield extends StatelessWidget {
  const IconTextfield(
      {super.key,
      required this.textController,
      required this.hintText,
      required this.icon,
      this.onChanged,
      this.hintStyle});

  final TextEditingController textController;
  final String hintText;
  final Widget icon;
  final Function(String)? onChanged;
  final TextStyle? hintStyle;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(4.gw, 0, 0, 0),
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        children: [
          icon,
          Expanded(
            child: TextField(
              controller: textController,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: hintStyle,
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: context.colorTheme.borderA,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12.gw),
                    bottomRight: Radius.circular(12.gw),
                  ),
                ),
              ),
              onChanged: onChanged,
              style: context.textTheme.regular.fs16,
            ),
          ),
        ],
      ),
    );
  }
}
