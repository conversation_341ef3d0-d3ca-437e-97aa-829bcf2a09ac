import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';

import 'modify_pwd_cubit.dart';
import 'modify_pwd_state.dart';

enum SetPasswordType { modifyLoginPwd, modifyFundPwd, setFundPwd }

class ModifyPwdPage extends BasePage {
  final SetPasswordType type;

  const ModifyPwdPage({super.key, required this.type});

  @override
  BasePageState<BasePage> getState() => _ModifyPwdPageState();
}

class _ModifyPwdPageState extends BasePageState<ModifyPwdPage> {
  final loginPwdFormatters = [
    LengthLimitingTextInputFormatter(22),
  ];

  final fundPwdFormatters = [
    LengthLimitingTextInputFormatter(6),
    FilteringTextInputFormatter.digitsOnly,
  ];

  bool isLoginType = false;

  @override
  void initState() {
    isLoginType = widget.type == SetPasswordType.modifyLoginPwd;
    switch (widget.type) {
      case SetPasswordType.modifyLoginPwd:
        pageTitle = '修改登录密码'.tr();
        break;

      case SetPasswordType.modifyFundPwd:
        pageTitle = '修改资金密码'.tr();
        break;

      case SetPasswordType.setFundPwd:
        pageTitle = '设置资金密码'.tr();
        break;
    }
    super.initState();
  }

  Widget _getPasswordTextField({
    required String title,
    required TextEditingController controller,
    required bool isPwdVisible,
    required Function(String) onChanged,
    required VoidCallback onToggleVisibility,
    required String hintText,
    required TextInputType keyboardType,
    required List<TextInputFormatter> inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.headlineSmall),
        SizedBox(height: 5.gw),
        CommonTextField(
          controller: controller,
          hintText: hintText,
          hintTextColor: Theme.of(context).textTheme.labelSmall?.color,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: onChanged,
          obscureText: !isPwdVisible,
          suffixIcon: GestureDetector(
            onTap: onToggleVisibility,
            child: SizedBox(
              width: 20.gw,
              child: SvgPicture.asset(
                "assets/images/login/icon_password_${isPwdVisible ? "" : "in"}visible.svg",
                width: 24.gw,
                height: 24.gw,
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget buildOldPwdTextField() {
    final title = widget.type == SetPasswordType.setFundPwd
        ? "验证登录密码"
        : isLoginType
            ? '旧登录密码'
            : '旧资金密码';
    final hintText = widget.type == SetPasswordType.setFundPwd
        ? 'hint_enter_password'
        : isLoginType
            ? 'hint_enter_password'
            : 'hint_enter_fund_password';
    final keyboardType = widget.type != SetPasswordType.modifyFundPwd ? TextInputType.text : TextInputType.number;
    final inputFormatters = widget.type != SetPasswordType.modifyFundPwd ? loginPwdFormatters : fundPwdFormatters;

    return BlocBuilder<ModifyPwdCubit, ModifyPwdState>(
      buildWhen: (previous, current) => previous.isOldPwdVisible != current.isOldPwdVisible,
      builder: (context, state) {
        return _getPasswordTextField(
          title: title.tr(),
          controller: state.oldPwdController,
          isPwdVisible: state.isOldPwdVisible,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: (value) => context.read<ModifyPwdCubit>().oldPwdChanged(value),
          onToggleVisibility: () => context.read<ModifyPwdCubit>().toggleOldPwdVisibility(),
          hintText: hintText.tr(),
        );
      },
    );
  }

  Widget buildNewPwdTextField() {
    final title = isLoginType ? '新登录密码' : '新资金密码';
    final hintText = isLoginType ? 'hint_enter_password' : 'hint_enter_fund_password';
    final keyboardType = isLoginType ? TextInputType.text : TextInputType.number;
    final inputFormatters = isLoginType ? loginPwdFormatters : fundPwdFormatters;

    return BlocBuilder<ModifyPwdCubit, ModifyPwdState>(
      buildWhen: (previous, current) => previous.isNewPwdVisible != current.isNewPwdVisible,
      builder: (context, state) {
        return _getPasswordTextField(
          title: title.tr(),
          controller: state.newPwdController,
          isPwdVisible: state.isNewPwdVisible,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: (value) => context.read<ModifyPwdCubit>().newPwdChanged(value),
          onToggleVisibility: () => context.read<ModifyPwdCubit>().toggleNewPwdVisibility(),
          hintText: hintText.tr(),
        );
      },
    );
  }

  Widget buildNewPwdConfirmTextField() {
    final title = isLoginType ? '新登录密码' : '新资金密码';
    final hintText = isLoginType ? 'hint_enter_password' : 'hint_enter_fund_password';
    final keyboardType = isLoginType ? TextInputType.text : TextInputType.number;
    final inputFormatters = isLoginType ? loginPwdFormatters : fundPwdFormatters;

    return BlocBuilder<ModifyPwdCubit, ModifyPwdState>(
      buildWhen: (previous, current) => previous.isNewPwdConfirmVisible != current.isNewPwdConfirmVisible,
      builder: (context, state) {
        return _getPasswordTextField(
          title: title.tr(),
          controller: state.newPwdConfirmController,
          isPwdVisible: state.isNewPwdConfirmVisible,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: (value) => context.read<ModifyPwdCubit>().newPwdConfirmChanged(value),
          onToggleVisibility: () => context.read<ModifyPwdCubit>().toggleNewPwdConfirmVisibility(),
          hintText: hintText.tr(),
        );
      },
    );
  }

  Widget mainPageWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 15.gw, left: 20.gw, right: 20.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 15.gw),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3), // 阴影颜色
                  spreadRadius: 2, // 阴影扩散半径
                  blurRadius: 10, // 阴影模糊半径
                ),
              ],
              borderRadius: BorderRadius.circular(10),
            ), // 圆角
            child: Column(
              children: [
                buildOldPwdTextField(),
                SizedBox(height: 10.gw),
                buildNewPwdTextField(),
                SizedBox(height: 10.gw),
                buildNewPwdConfirmTextField(),
              ],
            ),
          ),
          SizedBox(
            height: 30.gw,
          ),
          CommonButton(
              title: "confirm".tr(),
              onPressed: () => context.read<ModifyPwdCubit>().onClickModifyPassword(widget.type)),
        ],
      ),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return mainPageWidget();
  }
}
